<!-- src/components/SupersetDashboard.vue -->
<template>
  <div class="superset-wrapper">
    <!-- 加载 / 错误占位 -->
    <div v-if="loading || error" class="placeholder">
      <span v-if="loading">Dashboard 加载中…</span>
      <span v-else-if="error">加载失败，请刷新重试</span>
    </div>

    <!-- Superset 会把 iframe 插到这个容器 -->
    <div v-show="!loading && !error" ref="mountPoint" class="dashboard-container" />
  </div>
</template>

<script>
// 使用全局引入的 Superset Embedded SDK
// SDK 已通过 <script> 标签在 public/index.html 中引入
export default {
  name: 'SupersetDashboard',
  props: {
    /* Superset 域名 */
    domain: {
      type: String,
      default: 'http://*************:8080'
    },
    /* 仪表盘 id */
    dashboardId: {
      type: String,
      required: true
    },
    /* 是否隐藏标题 */
    hideTitle: {
      type: Boolean,
      default: true
    },
    /* 过滤器是否默认展开 */
    filtersExpanded: {
      type: Boolean,
      default: true
    },
    /* 透传给 Superset 的 url 查询参数 */
    urlParams: {
      type: Object,
      default: () => ({})
    },
    /* 由父组件注入的 token 获取函数 */
    fetchGuestToken: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      loading: true,
      error: false
    }
  },
  mounted() {
    this.initDashboard()
  },
  methods: {
    /* 初始化仪表盘 */
    async initDashboard() {
      console.log('initDashboard')
      try {
        // 检查全局 SDK 是否可用
        if (typeof window.supersetEmbeddedSdk === 'undefined') {
          throw new Error('Superset Embedded SDK not loaded. Please ensure the script is included in index.html')
        }

        const { embedDashboard } = window.supersetEmbeddedSdk

        await embedDashboard({
          id: this.dashboardId,
          supersetDomain: this.domain.replace(/\/$/, ''),
          mountPoint: this.$refs.mountPoint,
          fetchGuestToken: this.fetchGuestToken, // 来自父组件
          dashboardUiConfig: {
            hideTitle: this.hideTitle,
            filters: { expanded: this.filtersExpanded },
            urlParams: this.urlParams
          },
          iframeSandboxExtras: [
            'allow-top-navigation',
            'allow-popups-to-escape-sandbox'
          ],
          debug: process.env.NODE_ENV === 'development'
        })
        this.loading = false
      } catch (e) {
        console.error('[SupersetDashboard] 初始化失败', e)
        this.loading = false
        this.error = true
      }
    }
  }
}
</script>

<style>
.superset-wrapper {
  width: 100%;
  height: 100%;
}

.dashboard-container {
  width: 100%;
  height: 100vh;
}

/* 全局样式，确保iframe能够正确应用样式 */
.dashboard-container iframe {
  height: 100vh !important;
  width: 100% !important;
  border: none !important;
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 确保父容器也没有额外的边距和填充 */
.superset-wrapper,
.dashboard-container {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-family: Arial, sans-serif;
  color: #666;
  font-size: 16px;
}
</style>
